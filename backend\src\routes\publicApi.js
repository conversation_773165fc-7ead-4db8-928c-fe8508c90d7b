const express = require('express');
const router = express.Router();

// Import controllers
const tourController = require('../controllers/tourController');
const categoryController = require('../controllers/categoryController');
const departureController = require('../controllers/departureController');
const destinationController = require('../controllers/destinationController');
const transportationController = require('../controllers/transportationController');

// Public Tours API
router.get('/tours', async (req, res) => {
    try {
        // Gọi hàm getAllTours từ tourController với req và res
        await tourController.getAllTours(req, res);
    } catch (error) {
        console.error('Error in public tours API:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi lấy danh sách tours',
            error: error.message
        });
    }
});

// Public Tour Detail API
router.get('/tours/:id', async (req, res) => {
    try {
        await tourController.getTourById(req, res);
    } catch (error) {
        console.error('Error in public tour detail API:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi lấy chi tiết tour',
            error: error.message
        });
    }
});

// Public Categories API
router.get('/categories', async (req, res) => {
    try {
        await categoryController.getAll(req, res);
    } catch (error) {
        console.error('Error in public categories API:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi lấy danh sách categories',
            error: error.message
        });
    }
});

// Public Departures API
router.get('/departures', async (req, res) => {
    try {
        await departureController.getAll(req, res);
    } catch (error) {
        console.error('Error in public departures API:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi lấy danh sách departures',
            error: error.message
        });
    }
});

// Public Destinations API
router.get('/destinations', async (req, res) => {
    try {
        await destinationController.getAll(req, res);
    } catch (error) {
        console.error('Error in public destinations API:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi lấy danh sách destinations',
            error: error.message
        });
    }
});

// Public Transportations API
router.get('/transportations', async (req, res) => {
    try {
        await transportationController.getAll(req, res);
    } catch (error) {
        console.error('Error in public transportations API:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi lấy danh sách transportations',
            error: error.message
        });
    }
});

module.exports = router;
