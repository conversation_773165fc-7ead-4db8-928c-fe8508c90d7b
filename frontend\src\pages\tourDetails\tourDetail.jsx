import React, { useEffect, useState } from 'react'
import { Navigate, useNavigate, useParams } from 'react-router-dom'
import { Calendar, Collapse, theme } from 'antd';
import { CaretRightOutlined } from '@ant-design/icons';
import { motion } from 'framer-motion';
import { tourApi } from '../../api/tourApi';
import ImageSlider from './imageSlider';
import Map from '../../assets/images/map.png'
import Eat from '../../assets/images/eat.png'
import Friend from '../../assets/images/friend.png'
import Time from '../../assets/images/time2.png'
import Oto from '../../assets/images/oto.png'
import Sale from '../../assets/images/sale.png'
import Code from '../../assets/images/code.png'
import Vitri from '../../assets/images/vitri.png'
import Calenda from '../../assets/images/celanda.png'
import Time2 from '../../assets/images/time.png'
import Concho from '../../assets/images/concho.png'

const { Panel } = Collapse;

function TourDetails() {
    const { slug } = useParams();
    const [tourDetails, setTourDetails] = useState([]);
    const [showImageSlider, setShowImageSlider] = useState(false);
    const [departureDates, setDepartureDates] = useState([]);
    const navigate = useNavigate();

    useEffect(() => {
        const fetchData = async () => {
            try {
                const response = await tourApi.getTourDetailBySlug(slug);
                setTourDetails(response);
                const dates = response?.tour?.tourDetail?.map(detail => detail.dayStart) || [];
                setDepartureDates(dates);
            } catch (error) {
                console.error('Error fetching tour details:', error);
            }
        }
        fetchData();
    }, [slug]);

    const dateCellRender = (value) => {
        const formattedDate = value.format('YYYY-MM-DD');
        if (departureDates.includes(formattedDate)) {
            return (
                <div style={{ textAlign: 'center', color: '#fff', backgroundColor: '#1890ff', borderRadius: '50%' }}>
                    Ngày khởi hành
                </div>
            );
        }
        return null;
    };

    const onPanelChange = (value, mode) => {
        console.log(value.format('YYYY-MM-DD'), mode);
    };

    const { token } = theme.useToken();
    const panelStyle = {
        marginBottom: 22,
        background: token.colorFillAlter,
        borderRadius: token.borderRadiusLG,
        border: 'none',
    };

    const scheduleItems =
        tourDetails?.tour?.schedule?.map((item) => ({
            key: item.id,
            label: `Ngày ${item.day}: ${item.title}`,
            children: <p>{item.information}</p>,
            style: panelStyle
        })) || [];

    return (
        <div className='flex flex-col justify-start items-center relative w-full'>
            <div className="flex flex-col justify-between items-center w-full">
                <div className="flex flex-col justify-between items-center gap-8 w-full">
                    {/* Header */}
                    <div className="mx-auto px-4 w-[85%]">
                        <div className="mb-5">
                            <div className="flex items-center justify-start w-full">
                                <p className='text-lg font-semibold text-gray-900 cursor-pointer' onClick={() => navigate('/')}>
                                    Du lịch / 
                                </p>
                                <p className='text-[#0b5da7] pointer-events-none text-lg no-underline font-bold ml-1'>
                                    {tourDetails?.tour?.title}
                                </p>
                            </div>
                            <h2 className="text-4xl mb-5 leading-16">
                                {tourDetails?.tour?.title}
                            </h2>
                        </div>
                    </div>

                    {/* Content Container */}
                    <div className="w-[85%]">
                        <div className="flex gap-12">
                            {/* Left Content */}
                            <div className="w-[70%] flex flex-col gap-16">
                                {/* Image Gallery */}
                                <div className="w-full max-h-[21rem]">
                                    <div className="grid grid-cols-5 auto-rows-[minmax(0,52rem)] gap-2.5 h-full">
                                        <div className="col-span-1 flex flex-col justify-start items-start gap-2">
                                            {tourDetails?.tour?.images?.map((item, key) => (
                                                <div 
                                                    className="relative h-1/2 w-full cursor-pointer" 
                                                    onClick={() => setShowImageSlider(true)} 
                                                    key={key}
                                                >
                                                    <img 
                                                        src={item.source} 
                                                        alt="" 
                                                        className="object-cover transition-all duration-300 w-full h-full rounded-[20px]"
                                                    />
                                                </div>
                                            ))}
                                        </div>
                                        <div className="col-span-4 w-full h-full cursor-pointer" onClick={() => setShowImageSlider(true)}>
                                            <img 
                                                src={tourDetails?.tour?.images?.[0]?.source} 
                                                alt="" 
                                                className="object-cover rounded-[20px] w-full h-full"
                                            />
                                        </div>
                                    </div>
                                </div>

                                {/* Calendar */}
                                <div className="tour-calendar">
                                    <div className="section-detail">
                                        <h3 className="text-3xl font-bold uppercase text-center mb-5">
                                            Lịch khởi hành
                                        </h3>
                                        <div className="calendar">
                                            <Calendar dateCellRender={dateCellRender} />
                                        </div>
                                    </div>
                                </div>

                                {/* Overview */}
                                <div className="overview">
                                    <div className="w-full">
                                        <h3 className="text-3xl font-bold uppercase text-center mb-5">
                                            Thông tin thêm về chuyến đi
                                        </h3>
                                        <div className="grid grid-cols-3 gap-5">
                                            <div className="flex flex-col gap-3 mb-4">
                                                <img src={Map} alt="" className="w-[50px]" />
                                                <h3 className="text-xl font-bold leading-tight mt-2">
                                                    Điểm tham quan
                                                </h3>
                                                <p className="m-0 text-lg font-medium">
                                                    {tourDetails?.tour?.information?.attractions}
                                                </p>
                                            </div>
                                            <div className="flex flex-col gap-3 mb-4">
                                                <img src={Eat} alt="" className="w-[50px]" />
                                                <h3 className="text-xl font-bold leading-tight mt-2">
                                                    Ẩm thực
                                                </h3>
                                                <p className="m-0 text-lg font-medium">
                                                    {tourDetails?.tour?.information?.cuisine}
                                                </p>
                                            </div>
                                            <div className="flex flex-col gap-3 mb-4">
                                                <img src={Friend} alt="" className="w-[50px]" />
                                                <h3 className="text-xl font-bold leading-tight mt-2">
                                                    Đối tượng thích hợp
                                                </h3>
                                                <p className="m-0 text-lg font-medium">
                                                    {tourDetails?.tour?.information?.suitableObject}
                                                </p>
                                            </div>
                                            <div className="flex flex-col gap-3 mb-4">
                                                <img src={Time} alt="" className="w-[50px]" />
                                                <h3 className="text-xl font-bold leading-tight mt-2">
                                                    Thời gian lý tưởng
                                                </h3>
                                                <p className="m-0 text-lg font-medium">
                                                    {tourDetails?.tour?.information?.idealTime}
                                                </p>
                                            </div>
                                            <div className="flex flex-col gap-3 mb-4">
                                                <img src={Oto} alt="" className="w-[50px]" />
                                                <h3 className="text-xl font-bold leading-tight mt-2">
                                                    Phương tiện
                                                </h3>
                                                <p className="m-0 text-lg font-medium">
                                                    {tourDetails?.tour?.information?.vehicle}
                                                </p>
                                            </div>
                                            <div className="flex flex-col gap-3 mb-4">
                                                <img src={Sale} alt="" className="w-[50px]" />
                                                <h3 className="text-xl font-bold leading-tight mt-2">
                                                    Khuyến mãi
                                                </h3>
                                                <p className="m-0 text-lg font-medium">
                                                    {tourDetails?.tour?.information?.promotion}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Schedule */}
                                <div className="mb-[50px]">
                                    <div className="section-detail">
                                        <h3 className="text-3xl font-bold uppercase text-center mb-5">
                                            Lịch trình
                                        </h3>
                                        <Collapse 
                                            className='collapse' 
                                            items={scheduleItems} 
                                            accordion 
                                            style={{ width: '100%', margin: 'auto', background: token.colorBgContainer }}
                                            bordered={false}
                                            defaultActiveKey={[]}
                                            expandIcon={({ isActive }) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}
                                        />
                                    </div>
                                </div>
                            </div>

                            {/* Right Content */}
                            <div className="flex-[0_0_30%]">
                                <div className="sticky top-40">
                                    <div className="rounded-2xl shadow-[0_0_10px_0_hsla(0,0%,9%,.12)] p-6">
                                        {/* Price Section */}
                                        <div className="mb-4">
                                            <div className="flex justify-between flex-row items-center">
                                                <h4 className="text-2xl font-bold text-black">Giá:</h4>
                                                <div className="flex justify-center items-center gap-4">
                                                    <p className="text-gray-500">
                                                        <span className="line-through">6.490.000 đ</span> / Khách
                                                    </p>
                                                </div>
                                            </div>
                                            <div className="text-[#e01600] text-3xl font-semibold">
                                                5.990.000 đ <span className="text-2xl font-semibold text-gray-900">/ Khách</span>
                                            </div>
                                        </div>

                                        {/* Tour Info */}
                                        <div className="tour-price-info">
                                            <div className="flex flex-col gap-2 my-4 mb-8">
                                                <div className="gap-4 flex flex-row justify-between items-center text-base font-bold">
                                                    <div className="flex flex-row justify-center items-center gap-2">
                                                        <img src={Code} alt="" />
                                                        <p className="my-1 text-lg">
                                                            Mã tour: <span className="font-bold text-[#0b5da7] text-lg">{tourDetails?.tour?.code}</span>
                                                        </p>
                                                    </div>
                                                </div>
                                                <div className="gap-4 flex flex-row justify-between items-center text-base font-bold">
                                                    <div className="flex flex-row justify-center items-center gap-2">
                                                        <img src={Vitri} alt="" />
                                                        <p className="my-1 text-lg">
                                                            Khởi hành: <span className="font-bold text-[#0b5da7] text-lg">{tourDetails?.tour?.departure}</span>
                                                        </p>
                                                    </div>
                                                </div>
                                                <div className="gap-4 flex flex-row justify-between items-center text-base font-bold">
                                                    <div className="flex flex-row justify-center items-center gap-2">
                                                        <img src={Calenda} alt="" />
                                                        <p className="my-1 text-lg">
                                                            Ngày khởi hành: <span className="font-bold text-[#0b5da7] text-lg">31-12-2024</span>
                                                        </p>
                                                    </div>
                                                </div>
                                                <div className="gap-4 flex flex-row justify-between items-center text-base font-bold">
                                                    <div className="flex flex-row justify-center items-center gap-2">
                                                        <img src={Time2} alt="" />
                                                        <p className="my-1 text-lg">
                                                            Thời gian: <span className="font-bold text-[#0b5da7] text-lg">4N3Đ</span>
                                                        </p>
                                                    </div>
                                                </div>
                                                <div className="gap-4 flex flex-row justify-between items-center text-base font-bold">
                                                    <div className="flex flex-row justify-center items-center gap-2">
                                                        <img src={Concho} alt="" />
                                                        <p className="my-1 text-lg">
                                                            Số chỗ còn <span className="font-bold text-[#0b5da7] text-lg">9 chỗ</span>
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Booking Buttons */}
                                        <div className="flex flex-row justify-center items-center gap-4">
                                            <button className='w-full normal-case py-4 px-0 cursor-pointer rounded-lg flex-1 bg-white text-[#e01600] border border-[#e01600] font-bold'>
                                                Ngày khác
                                            </button>
                                            <button 
                                                className='w-full bg-[#e01600] text-white normal-case py-4 px-0 border-none cursor-pointer rounded-lg flex-[2_1] font-bold hover:bg-[#831104]'
                                                onClick={() => navigate('/order', { state: { tourDetails } })}
                                            >
                                                Đặt tour
                                            </button>
                                        </div>
                                    </div>

                                    {/* Contact Section */}
                                    <div className="flex flex-col justify-end items-end mt-6 w-full">
                                        <div className="max-w-full flex gap-2 text-lg font-bold italic">
                                            <button className='bg-[#0b5da7] text-white border-none flex flex-row justify-center items-center py-3 px-3 cursor-pointer rounded-lg no-underline not-italic hover:bg-[#0d70c6]'>
                                                <i className="fa-solid fa-phone-volume"></i>
                                                <p className="flex-[1.3_1] m-0 font-bold text-xs ml-2">
                                                    Gọi miễn phí qua internet
                                                </p>
                                            </button>
                                            <button className='bg-white text-[#0b5da7] border border-[#0b5da7] flex justify-center items-center py-3 px-3 cursor-pointer rounded-lg gap-1'>
                                                <i className="fa-regular fa-envelope"></i>
                                                <p className="flex-[1.3_1] m-0 font-bold text-xs">
                                                    Liên hệ tư vấn
                                                </p>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Image Slider Overlay */}
            {showImageSlider && (
                <div className='fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 flex justify-center items-center z-10' onClick={() => setShowImageSlider(false)}>
                    <motion.div
                        className='relative z-[220]'
                        onClick={(e) => e.stopPropagation()}
                        animate={{ opacity: 1, scale: 1 }}
                        initial={{ opacity: 0, scale: 0.8 }}
                        exit={{ opacity: 0, scale: 0.8 }}
                        transition={{ duration: 0.3 }}
                    >
                        <ImageSlider image={tourDetails?.tour?.images} onCancel={() => setShowImageSlider(false)} />
                    </motion.div>
                </div>
            )}
        </div>
    )
}

export default TourDetails