<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API</title>
</head>
<body>
    <h1>Test API</h1>
    <div id="results"></div>

    <script>
        async function testAPI() {
            const resultsDiv = document.getElementById('results');
            
            try {
                // Test tours API
                console.log('Testing tours API...');
                const toursResponse = await fetch('http://localhost:3000/api/public/tours?highlight=true&limit=3');
                const toursData = await toursResponse.json();
                console.log('Tours data:', toursData);
                
                // Test categories API
                console.log('Testing categories API...');
                const categoriesResponse = await fetch('http://localhost:3000/api/public/categories?limit=5');
                const categoriesData = await categoriesResponse.json();
                console.log('Categories data:', categoriesData);
                
                // Test destinations API
                console.log('Testing destinations API...');
                const destinationsResponse = await fetch('http://localhost:3000/api/public/destinations?limit=5');
                const destinationsData = await destinationsResponse.json();
                console.log('Destinations data:', destinationsData);
                
                resultsDiv.innerHTML = `
                    <h2>API Test Results</h2>
                    <h3>Tours (${toursData.data?.length || 0} items):</h3>
                    <pre>${JSON.stringify(toursData, null, 2)}</pre>
                    
                    <h3>Categories (${categoriesData.data?.length || 0} items):</h3>
                    <pre>${JSON.stringify(categoriesData, null, 2)}</pre>
                    
                    <h3>Destinations (${destinationsData.data?.length || 0} items):</h3>
                    <pre>${JSON.stringify(destinationsData, null, 2)}</pre>
                `;
                
            } catch (error) {
                console.error('API test error:', error);
                resultsDiv.innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
            }
        }
        
        // Run test when page loads
        testAPI();
    </script>
</body>
</html>
