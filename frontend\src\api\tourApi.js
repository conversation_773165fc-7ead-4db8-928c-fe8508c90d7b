import { baseApi } from './baseApi'

export const tourApi = {
  // Lấy tất cả tours (có thể public hoặc private tùy params)
  getAllTours: async (params) => {
    try {
      // Sử dụng baseApi để gọi public API
      const response = await baseApi.get('/api/public/tours', { params })
      return response.data
    } catch (error) {
      throw error
    }
  },

  // <PERSON><PERSON>y tours theo slug/category (public)
  getToursBySlug: async (slug, params) => {
    try {
      const response = await baseApi.get(`/api/public/tours/${slug}`, { params })
      return response.data
    } catch (error) {
      throw error
    }
  },

  // Lấy chi tiết tour (public)
  getTourDetail: async (id) => {
    try {
      const response = await baseApi.get(`/api/public/tours/${id}`)
      return response.data
    } catch (error) {
      throw error
    }
  },

  // Lấy departures (public)
  getDepartures: async () => {
    try {
      const response = await baseApi.get('/api/public/departures')
      return response.data
    } catch (error) {
      throw error
    }
  },

  // Lấy destinations (public)
  getDestinations: async () => {
    try {
      const response = await baseApi.get('/api/public/destinations')
      return response.data
    } catch (error) {
      throw error
    }
  },

  // Lấy transportations (public)
  getTransportations: async () => {
    try {
      const response = await baseApi.get('/api/public/transportations')
      return response.data
    } catch (error) {
      throw error
    }
  },

  // Lấy categories (public)
  getCategories: async () => {
    try {
      const response = await baseApi.get('/api/public/categories')
      return response.data
    } catch (error) {
      throw error
    }
  },

  // Tìm kiếm tours (public)
  searchTours: async (searchParams) => {
    try {
      const response = await baseApi.get('/api/public/tours', { params: searchParams })
      return response.data
    } catch (error) {
      throw error
    }
  }
}

export default tourApi
