import { baseApi } from './baseApi'

export const tourApi = {
  // L<PERSON>y tất cả tours (có thể public hoặc private tùy params)
  getAllTours: async (params) => {
    try {
      // Sử dụng baseApi để gọi public API
      const response = await baseApi.get('/api/public/tours', { params })
      return response.data
    } catch (error) {
      throw error
    }
  },

  // <PERSON><PERSON><PERSON> tours theo category slug (public)
  getToursBySlug: async (slug, params) => {
    try {
      const response = await baseApi.get(`/api/public/tours/category/${slug}`, { params })
      return response.data
    } catch (error) {
      throw error
    }
  },

  // L<PERSON>y chi tiết tour theo ID (public)
  getTourDetail: async (id) => {
    try {
      const response = await baseApi.get(`/api/public/tours/id/${id}`)
      return response.data
    } catch (error) {
      throw error
    }
  },

  // L<PERSON>y chi tiết tour theo slug (public)
  getTourDetailBySlug: async (slug) => {
    try {
      const response = await baseApi.get(`/api/public/tours/${slug}`)
      return response.data
    } catch (error) {
      throw error
    }
  },

  // Lấy departures (public)
  getDepartures: async () => {
    try {
      const response = await baseApi.get('/api/public/departures')
      return response.data
    } catch (error) {
      throw error
    }
  },

  // Lấy destinations (public)
  getDestinations: async () => {
    try {
      const response = await baseApi.get('/api/public/destinations')
      return response.data
    } catch (error) {
      throw error
    }
  },

  // Lấy transportations (public)
  getTransportations: async () => {
    try {
      const response = await baseApi.get('/api/public/transportations')
      return response.data
    } catch (error) {
      throw error
    }
  },

  // Lấy categories (public)
  getCategories: async () => {
    try {
      const response = await baseApi.get('/api/public/categories')
      return response.data
    } catch (error) {
      throw error
    }
  },

  // Tìm kiếm tours (public)
  searchTours: async (searchParams) => {
    try {
      const response = await baseApi.get('/api/public/tours', { params: searchParams })
      return response.data
    } catch (error) {
      throw error
    }
  }
}

export default tourApi
